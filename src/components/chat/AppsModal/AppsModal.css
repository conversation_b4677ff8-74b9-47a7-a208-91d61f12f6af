/* Apps Modal Custom Sizing */
.apps-modal {
  min-width: 80vw !important;
  min-height: 80vh;
  padding: 26px;
}
.apps-modal .modal__header{
  padding: 0 !important;
}
.apps-modal .modal__content {
  padding: 24px;
  height: calc(100% - 80px); /* Subtract header height */
  overflow-y: auto;
}

/* Apps Modal Content */
.apps-modal__content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Placeholder Content */
.apps-modal__placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--text-secondary);
}

.apps-modal__placeholder-icon {
  margin-bottom: 24px;
  color: var(--text-muted);
}

.apps-modal__placeholder-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
}

.apps-modal__placeholder-description {
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-secondary);
  margin: 0;
  max-width: 400px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .apps-modal {
    width: 95vw !important;
    height: 95vh;
    margin: 0 !important;
    padding: 18px !important;
  }
  
  .apps-modal .modal__content {
    padding: 20px;
    height: calc(100% - 70px); /* Adjust for smaller header on mobile */
  }
  
  .apps-modal__placeholder-title {
    font-size: 20px;
  }
  
  .apps-modal__placeholder-description {
    font-size: 14px;
  }
  
  .apps-modal__placeholder-icon {
    margin-bottom: 20px;
  }
}
