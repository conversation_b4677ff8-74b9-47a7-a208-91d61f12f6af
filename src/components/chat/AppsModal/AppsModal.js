import React from 'react';
import Modal from '../../common/Modal';
import './AppsModal.css';

const AppsModal = ({ isOpen, onClose }) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Apps"
      size="custom"
      className="apps-modal"
      closeOnOverlayClick={true}
    >
      <div className="apps-modal__content">
        <div className="apps-modal__placeholder">
          <div className="apps-modal__placeholder-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
              <rect x="3" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
              <rect x="14" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
              <rect x="3" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
              <rect x="14" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </div>
          <h3 className="apps-modal__placeholder-title">Apps Coming Soon</h3>
          <p className="apps-modal__placeholder-description">
            We're working on bringing you powerful apps and integrations to enhance your chat experience.
            Stay tuned for exciting updates!
          </p>
        </div>
      </div>
    </Modal>
  );
};

export default AppsModal;
